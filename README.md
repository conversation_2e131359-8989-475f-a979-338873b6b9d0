# Stock Diff Location Sync

A Python tool for synchronizing inventory stock levels and location data between an external vendor API and WooCommerce stores. The tool performs differential updates, only modifying products when changes are detected, making it efficient for large inventories.

## Features

- **Differential Sync**: Only updates products when stock levels or locations actually change
- **Batch Processing**: Efficiently processes updates in configurable batches
- **Robust Error Handling**: Includes retry logic and exponential backoff
- **Comprehensive Logging**: Detailed logs with timestamps for monitoring and debugging
- **Location Management**: Syncs product availability across multiple warehouse locations
- **SSL Support**: Secure HTTPS connections with proper certificate verification

## Requirements

- Python 3.7+
- WooCommerce REST API access
- External vendor API credentials

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/stoc_diff_loc.git
cd stoc_diff_loc
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Copy the environment template and configure your settings:
```bash
cp .env.example .env
```

4. Edit `.env` with your actual API credentials and URLs.

## Configuration

Create a `.env` file in the project root with the following variables:

```env
# External Vendor API Configuration
VENDOR_API_URL=https://your-vendor-api.com/stock
EXTERNAL_API_USERNAME=your_username
EXTERNAL_API_PASSWORD=your_password

# WooCommerce Configuration
WP_URL=https://your-woocommerce-site.com
CONSUMER_KEY=ck_your_consumer_key
CONSUMER_SECRET=cs_your_consumer_secret
```

## Usage

Run the synchronization:

```bash
python stoc_diff_loc.py
```

The tool will:
1. Fetch current stock data from the external vendor API
2. Retrieve existing product data from WooCommerce
3. Compare the data to identify changes
4. Update only the products that have changed
5. Generate a detailed report of the sync operation

## Valid Locations

The tool recognizes the following warehouse/location codes:
- BOT, D1-D36 (excluding some numbers), D3N, D5N

## Logging

The tool creates timestamped log files for each run:
- Console output for real-time monitoring
- File logging: `stock_sync_YYYYMMDD_HHMMSS.log`

## Error Handling

- Automatic retries for failed API requests
- Exponential backoff for rate limiting
- Detailed error logging
- Graceful handling of network timeouts

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

If you encounter any issues or have questions, please open an issue on GitHub.
