#!/usr/bin/env python3
"""
Quick test script to verify API connections work with your credentials.
"""

import requests
from config import Config
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_vendor_api():
    """Test connection to vendor API"""
    try:
        config = Config.get_config()
        logger.info("Testing vendor API connection...")
        
        response = requests.get(
            config['vendor_api_url'],
            auth=(config['external_api_username'], config['external_api_password']),
            timeout=10
        )
        
        logger.info(f"Vendor API Status Code: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            stock_list = data.get('stockList', [])
            logger.info(f"✅ Vendor API connection successful! Found {len(stock_list)} items")
            if stock_list:
                logger.info(f"Sample item: {stock_list[0]}")
        else:
            logger.error(f"❌ Vendor API returned status {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ Vendor API connection failed: {e}")

def test_woocommerce_api():
    """Test connection to WooCommerce API"""
    try:
        config = Config.get_config()
        logger.info("Testing WooCommerce API connection...")
        
        # Test with a simple products endpoint
        response = requests.get(
            f"{config['wp_url']}/wp-json/wc/v3/products",
            auth=(config['consumer_key'], config['consumer_secret']),
            params={'per_page': 1},
            timeout=10
        )
        
        logger.info(f"WooCommerce API Status Code: {response.status_code}")
        if response.status_code == 200:
            products = response.json()
            logger.info(f"✅ WooCommerce API connection successful! Found products")
            if products:
                logger.info(f"Sample product: {products[0].get('name', 'N/A')} (SKU: {products[0].get('sku', 'N/A')})")
        else:
            logger.error(f"❌ WooCommerce API returned status {response.status_code}")
            logger.error(f"Response: {response.text}")
            
    except Exception as e:
        logger.error(f"❌ WooCommerce API connection failed: {e}")

if __name__ == "__main__":
    logger.info("🔍 Testing API connections...")
    test_vendor_api()
    print()
    test_woocommerce_api()
    print()
    logger.info("🏁 Connection tests completed!")
