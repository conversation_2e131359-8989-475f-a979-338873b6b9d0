import unittest
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add the parent directory to the path so we can import our module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from stoc_diff_loc import StockDiffSync, ProductState


class TestStockDiffSync(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.config = {
            'vendor_api_url': 'https://test-api.com/stock',
            'external_api_username': 'test_user',
            'external_api_password': 'test_pass',
            'wp_url': 'https://test-wp.com',
            'consumer_key': 'test_key',
            'consumer_secret': 'test_secret'
        }
        
    @patch('stoc_diff_loc.requests.Session')
    def test_init(self, mock_session):
        """Test StockDiffSync initialization."""
        syncer = StockDiffSync(self.config)
        
        self.assertEqual(syncer.config, self.config)
        self.assertIsNotNone(syncer.valid_locations)
        self.assertIn('BOT', syncer.valid_locations)
        self.assertIn('D1', syncer.valid_locations)
        
    def test_product_state(self):
        """Test ProductState dataclass."""
        state = ProductState(sku="TEST123", stock=10, locations={'D1', 'D2'})
        
        self.assertEqual(state.sku, "TEST123")
        self.assertEqual(state.stock, 10)
        self.assertEqual(state.locations, {'D1', 'D2'})
        
    @patch('stoc_diff_loc.requests.Session')
    def test_update_product_attributes(self, mock_session):
        """Test _update_product_attributes method."""
        syncer = StockDiffSync(self.config)
        
        existing_attributes = [
            {'id': 1, 'name': 'Color', 'slug': 'pa_color', 'options': ['Red', 'Blue']},
            {'id': 28, 'name': 'Lokacija', 'slug': 'pa_lokacija', 'options': ['D1']}
        ]
        
        new_locations = {'D1', 'D2', 'D3'}
        
        result = syncer._update_product_attributes(existing_attributes, new_locations)
        
        # Should preserve the color attribute
        self.assertEqual(result[0]['slug'], 'pa_color')
        
        # Should update the location attribute
        location_attr = next(attr for attr in result if attr['slug'] == 'pa_lokacija')
        self.assertEqual(set(location_attr['options']), new_locations)
        
    @patch('stoc_diff_loc.requests.Session')
    def test_valid_locations(self, mock_session):
        """Test that valid locations are properly defined."""
        syncer = StockDiffSync(self.config)
        
        # Test some known valid locations
        self.assertIn('BOT', syncer.valid_locations)
        self.assertIn('D1', syncer.valid_locations)
        self.assertIn('D35', syncer.valid_locations)
        self.assertIn('D3N', syncer.valid_locations)
        self.assertIn('D5N', syncer.valid_locations)
        
        # Test that it's a set (no duplicates)
        self.assertEqual(len(syncer.valid_locations), len(set(syncer.valid_locations)))


if __name__ == '__main__':
    unittest.main()
