#!/usr/bin/env python3
"""
Dry run test to verify the sync process works without making actual updates.
"""

import logging
from config import Config
from stoc_diff_loc import StockDiffSync

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_sync_process():
    """Test the sync process without making updates"""
    try:
        logger.info("🔍 Testing sync process (dry run)...")
        
        # Load configuration
        config = Config.get_config()
        Config.validate_config(config)
        logger.info("✅ Configuration loaded and validated")
        
        # Initialize syncer
        syncer = StockDiffSync(config)
        logger.info("✅ StockDiffSync initialized")
        
        # Test getting API state
        logger.info("📡 Testing vendor API data retrieval...")
        api_state = syncer.get_api_state()
        logger.info(f"✅ Retrieved {len(api_state)} products from vendor API")
        
        # Show sample data
        if api_state:
            sample_sku = list(api_state.keys())[0]
            sample_product = api_state[sample_sku]
            logger.info(f"Sample product: SKU={sample_product.sku}, Stock={sample_product.stock}, Locations={sample_product.locations}")
        
        # Test getting WooCommerce products
        logger.info("🛒 Testing WooCommerce data retrieval...")
        woo_products = syncer.get_woo_products()
        logger.info(f"✅ Retrieved {len(woo_products)} products from WooCommerce")
        
        # Show sample WooCommerce product
        if woo_products:
            sample_sku = list(woo_products.keys())[0]
            sample_woo = woo_products[sample_sku]
            logger.info(f"Sample WooCommerce product: SKU={sample_sku}, Stock={sample_woo.get('stock_quantity')}")
        
        # Count potential updates
        updates_needed = 0
        for sku, state in api_state.items():
            if sku in woo_products:
                woo_product = woo_products[sku]
                current_stock = woo_product.get('stock_quantity')
                if current_stock != state.stock:
                    updates_needed += 1
        
        logger.info(f"📊 Analysis complete: {updates_needed} products would need stock updates")
        logger.info("✅ Dry run test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Dry run test failed: {e}")
        raise

if __name__ == "__main__":
    test_sync_process()
