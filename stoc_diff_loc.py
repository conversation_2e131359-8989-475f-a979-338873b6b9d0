import requests
import base64
from typing import Dict, List, Set
import time
from datetime import datetime
import logging
from dataclasses import dataclass
from tqdm import tqdm
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'stock_sync_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class ProductState:
    sku: str
    stock: int
    locations: Set[str]

class StockDiffSync:
    def __init__(self, config: Dict):
        self.config = config
        self.valid_locations = Config.VALID_LOCATIONS

        # Setup WooCommerce auth using environment variables
        auth_str = f"{config['consumer_key']}:{config['consumer_secret']}"
        self.auth_header = f"Basic {base64.b64encode(auth_str.encode()).decode()}"

        # Setup session with SSL verification enabled (set to True)
        self.session = requests.Session()
        self.session.verify = True
        self.session.headers.update({
            'Authorization': self.auth_header,
            'Content-Type': 'application/json'
        })

        # Configure retry strategy
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["GET", "POST", "PUT", "PATCH"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)
        self.session.mount("http://", adapter)

        logger.info("StockDiffSync initialized")

    def get_api_state(self) -> Dict[str, ProductState]:
        """Get current state from external API"""
        try:
            logger.info("Fetching stock data from API...")
            response = self.session.get(
                self.config['vendor_api_url'],
                auth=(self.config['external_api_username'], self.config['external_api_password']),
                timeout=30
            )
            response.raise_for_status()
            vendor_data = response.json()

            products = {}
            for item in vendor_data.get('stockList', []):
                sku = str(item.get('item_code', '')).strip()
                vendor = str(item.get('vendor', '')).strip()
                try:
                    stock = int(float(item.get('stock_quantity', 0)))
                except (TypeError, ValueError):
                    stock = 0
                if not sku:
                    continue
                if sku not in products:
                    products[sku] = ProductState(sku=sku, stock=0, locations=set())
                products[sku].stock += stock
                if stock > 0 and vendor.upper() in self.valid_locations:
                    products[sku].locations.add(vendor.upper())
            return products
        except Exception as e:
            logger.error(f"Failed to get API state: {e}")
            raise

    def get_woo_products(self) -> Dict[str, Dict]:
        """Get WooCommerce products with key fields for diff checking"""
        try:
            all_products = {}
            page = 1
            per_page = 100
            while True:
                response = self.session.get(
                    f"{self.config['wp_url']}/wp-json/wc/v3/products",
                    params={
                        'per_page': per_page,
                        'page': page,
                        'status': 'publish',
                        '_fields': 'id,sku,attributes,stock_quantity,manage_stock,stock_status'
                    },
                    timeout=30
                )
                response.raise_for_status()
                products = response.json()
                if not products:
                    break
                for product in products:
                    sku = product.get('sku', '').strip()
                    if sku:
                        all_products[sku] = {
                            'id': product.get('id'),
                            'attributes': product.get('attributes', []),
                            'stock_quantity': product.get('stock_quantity'),
                            'manage_stock': product.get('manage_stock'),
                            'stock_status': product.get('stock_status')
                        }
                if len(products) < per_page:
                    break
                page += 1
                time.sleep(0.5)
            return all_products
        except Exception as e:
            logger.error(f"Failed to get WooCommerce products: {e}")
            raise

    def _update_product_attributes(self, existing_attributes: List[Dict], new_locations: Set[str]) -> List[Dict]:
        """Update only the location attribute while preserving others"""
        updated_attributes = [attr.copy() for attr in existing_attributes]
        location_attr_index = None
        attribute_id = Config.LOCATION_ATTRIBUTE_ID
        for i, attr in enumerate(updated_attributes):
            if attr.get('slug') == 'pa_lokacija':
                location_attr_index = i
                attribute_id = attr.get('id', Config.LOCATION_ATTRIBUTE_ID)
                break
        if new_locations:
            location_attribute = {
                'id': attribute_id,
                'name': 'Lokacija',
                'slug': 'pa_lokacija',
                'position': 0,
                'visible': True,
                'variation': False,
                'options': list(sorted(new_locations))
            }
            if location_attr_index is not None:
                updated_attributes[location_attr_index] = location_attribute
            else:
                updated_attributes.append(location_attribute)
        return updated_attributes

    def _process_batch_update(self, updates: List[Dict]) -> tuple:
        """Process a batch update using WooCommerce batch API with exponential backoff"""
        retries = 3
        wait_time = 2
        while retries > 0:
            try:
                batch_data = {'update': updates}
                response = self.session.post(
                    f"{self.config['wp_url']}/wp-json/wc/v3/products/batch",
                    json=batch_data,
                    timeout=60
                )
                if response.status_code != 200:
                    logger.error(f"Batch update error: {response.status_code} - {response.text}")
                response.raise_for_status()
                result = response.json()
                success_count = len(result.get('update', []))
                logger.info(f"Batch update completed: {success_count} products updated")
                return success_count, 0
            except Exception as e:
                error_details = response.text if response else "No response"
                logger.error(f"Batch update failed: {e} - Response: {error_details}. Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
                retries -= 1
                wait_time *= 2  # exponential backoff
        failed_count = len(updates)
        failed_ids = [update.get('id') for update in updates]
        logger.error(f"Batch update failed after retries. Failed product IDs: {failed_ids}")
        return 0, failed_count

    def sync(self):
        """Main sync process with diff-checking to avoid unnecessary updates"""
        start_time = datetime.now()
        try:
            print("\nFetching current states...")
            api_state = self.get_api_state()
            woo_products = self.get_woo_products()
            updates = []
            for sku, state in api_state.items():
                if sku in woo_products:
                    woo_product = woo_products[sku]
                    # Determine if an update is needed
                    needs_update = False
                    # Compare stock quantities
                    current_stock = woo_product.get('stock_quantity')
                    if current_stock != state.stock:
                        needs_update = True
                    # Compare location attribute options
                    current_locations = set()
                    for attr in woo_product.get('attributes', []):
                        if attr.get('slug') == 'pa_lokacija':
                            current_locations = set(attr.get('options', []))
                            break
                    if current_locations != state.locations:
                        needs_update = True
                    if not needs_update:
                        continue  # Skip update if nothing changed
                    product_update = {
                        'id': woo_product['id'],
                        'stock_quantity': state.stock,
                        'manage_stock': True,
                        'stock_status': 'instock' if state.stock > 0 else 'outofstock',
                    }
                    if state.locations:
                        product_update['attributes'] = self._update_product_attributes(
                            woo_product.get('attributes', []),
                            state.locations
                        )
                    updates.append(product_update)
            print("\nUpdate Summary:")
            print(f"Total products to update: {len(updates)}")
            print("\nPerforming updates...")
            success_count = 0
            error_count = 0
            batch_size = 50  # Reduced batch size to ease server load
            for i in tqdm(range(0, len(updates), batch_size), desc="Updating products"):
                batch = updates[i:i + batch_size]
                batch_success, batch_error = self._process_batch_update(batch)
                success_count += batch_success
                error_count += batch_error
                # Adaptive delay between batches
                if batch_error > 0:
                    time.sleep(5)
                else:
                    time.sleep(1)
            end_time = datetime.now()
            duration = end_time - start_time
            summary = f"""
Sync Complete
----------------------------------------
Start time:     {start_time.strftime('%Y-%m-%d %H:%M:%S')}
End time:       {end_time.strftime('%Y-%m-%d %H:%M:%S')}
Duration:       {str(duration).split('.')[0]}

Total products processed: {len(updates)}
Successfully updated:     {success_count}
Failed updates:           {error_count}
----------------------------------------
"""
            print(summary)
            logger.info(summary)
        except Exception as e:
            logger.error(f"Sync failed: {e}")
            raise
        finally:
            self.session.close()

def main():
    try:
        # Load and validate configuration
        config = Config.get_config()
        Config.validate_config(config)

        # Initialize and run sync
        syncer = StockDiffSync(config)
        syncer.sync()

    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        logger.error("Please check your .env file and ensure all required variables are set")
        raise
    except Exception as e:
        logger.error(f"Sync failed: {e}")
        raise

if __name__ == "__main__":
    main()
