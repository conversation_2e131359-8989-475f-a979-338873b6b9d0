# Contributing to Stock Diff Location Sync

Thank you for your interest in contributing to this project! This document provides guidelines and information for contributors.

## Code of Conduct

This project adheres to a code of conduct. By participating, you are expected to uphold this code. Please report unacceptable behavior to the project maintainers.

## How to Contribute

### Reporting Bugs

Before creating bug reports, please check the existing issues to avoid duplicates. When creating a bug report, include:

- A clear and descriptive title
- Steps to reproduce the behavior
- Expected behavior
- Actual behavior
- Environment details (Python version, OS, etc.)
- Log files if applicable

### Suggesting Enhancements

Enhancement suggestions are welcome! Please provide:

- A clear and descriptive title
- A detailed description of the proposed enhancement
- Explain why this enhancement would be useful
- List any alternatives you've considered

### Pull Requests

1. Fork the repository
2. Create a feature branch from `main`:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. Make your changes
4. Add or update tests as needed
5. Ensure all tests pass:
   ```bash
   python -m pytest tests/
   ```
6. Update documentation if needed
7. Commit your changes with a clear commit message
8. Push to your fork:
   ```bash
   git push origin feature/amazing-feature
   ```
9. Open a Pull Request

## Development Setup

1. Clone your fork:
   ```bash
   git clone https://github.com/yourusername/stoc_diff_loc.git
   cd stoc_diff_loc
   ```

2. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   pip install pytest pytest-cov flake8  # Development dependencies
   ```

4. Set up your environment:
   ```bash
   cp .env.example .env
   # Edit .env with your test credentials
   ```

## Coding Standards

- Follow PEP 8 style guidelines
- Use meaningful variable and function names
- Add docstrings to functions and classes
- Keep functions focused and small
- Add type hints where appropriate

## Testing

- Write tests for new functionality
- Ensure all existing tests pass
- Aim for good test coverage
- Use descriptive test names

Run tests:
```bash
python -m pytest tests/
```

Run tests with coverage:
```bash
python -m pytest tests/ --cov=. --cov-report=html
```

## Documentation

- Update README.md if you change functionality
- Add docstrings to new functions and classes
- Update configuration documentation for new environment variables

## Commit Messages

Use clear and meaningful commit messages:

- Use the present tense ("Add feature" not "Added feature")
- Use the imperative mood ("Move cursor to..." not "Moves cursor to...")
- Limit the first line to 72 characters or less
- Reference issues and pull requests liberally after the first line

## Questions?

If you have questions about contributing, please open an issue or contact the maintainers.
