# GitHub Setup Guide

This guide will help you set up your Stock Diff Location Sync project on GitHub.

## Prerequisites

- Git installed on your system
- A GitHub account
- Your project files ready (which you already have!)

## Step 1: Initialize Git Repository

```bash
# Navigate to your project directory
cd /Users/<USER>/Desktop/stoc_diff_loc

# Initialize git repository
git init

# Add all files to staging
git add .

# Create initial commit
git commit -m "Initial commit: Stock Diff Location Sync tool"
```

## Step 2: Create GitHub Repository

1. Go to [GitHub.com](https://github.com)
2. Click the "+" icon in the top right corner
3. Select "New repository"
4. Fill in the repository details:
   - **Repository name**: `stoc_diff_loc` (or your preferred name)
   - **Description**: "A Python tool for synchronizing inventory stock levels and location data between an external vendor API and WooCommerce stores"
   - **Visibility**: Choose Public or Private
   - **DO NOT** initialize with README, .gitignore, or license (we already have these)
5. Click "Create repository"

## Step 3: Connect Local Repository to GitHub

```bash
# Add GitHub repository as remote origin
git remote add origin https://github.com/YOUR_USERNAME/stoc_diff_loc.git

# Push your code to GitHub
git branch -M main
git push -u origin main
```

Replace `YOUR_USERNAME` with your actual GitHub username.

## Step 4: Verify Upload

1. Go to your repository on GitHub
2. You should see all your files including:
   - README.md
   - requirements.txt
   - stoc_diff_loc.py
   - config.py
   - tests/
   - And all other files we created

## Step 5: Set Up GitHub Actions (Optional)

The CI/CD workflow is already configured in `.github/workflows/ci.yml`. It will automatically:
- Run tests on multiple Python versions
- Check code quality with linting
- Run security checks

## Step 6: Configure Repository Settings

1. Go to your repository settings
2. Under "General" → "Features":
   - Enable Issues (for bug reports and feature requests)
   - Enable Discussions (for community questions)
3. Under "Pages" (if you want to host documentation):
   - Set up GitHub Pages if desired

## Step 7: Add Repository Secrets (For CI/CD)

If you plan to use the GitHub Actions workflow with actual API testing:

1. Go to Settings → Secrets and variables → Actions
2. Add repository secrets for your environment variables:
   - `VENDOR_API_URL`
   - `EXTERNAL_API_USERNAME`
   - `EXTERNAL_API_PASSWORD`
   - `WP_URL`
   - `CONSUMER_KEY`
   - `CONSUMER_SECRET`

## Next Steps

1. **Update README.md**: Replace placeholder URLs with your actual GitHub repository URL
2. **Create releases**: Tag versions of your software for easy distribution
3. **Set up branch protection**: Require pull request reviews for the main branch
4. **Add collaborators**: If working with a team
5. **Create issues**: Document known bugs or planned features

## Useful Git Commands

```bash
# Check status
git status

# Add specific files
git add filename.py

# Commit changes
git commit -m "Description of changes"

# Push changes
git push

# Pull latest changes
git pull

# Create new branch
git checkout -b feature/new-feature

# Switch branches
git checkout main
```

## Troubleshooting

- **Authentication issues**: Use a personal access token instead of password
- **Large files**: Use Git LFS for files over 100MB
- **Merge conflicts**: Resolve conflicts manually and commit

Your project is now ready for GitHub! 🚀
