"""
Configuration management for Stock Diff Location Sync
"""
import os
from typing import Dict, Optional
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for managing environment variables and settings."""
    
    # Valid warehouse/location codes
    VALID_LOCATIONS = {
        'BOT', 'D1', 'D10', 'D11', 'D12', 'D13', 'D14', 'D15', 'D17', 'D18', 'D19',
        'D2', 'D20', 'D21', 'D22', 'D23', 'D24', 'D26', 'D27', 'D29', 'D30', 'D31',
        'D32', 'D33', 'D34', 'D35', 'D36', 'D3N', 'D4', 'D5N', 'D7', 'D8', 'D9'
    }
    
    # Default batch size for WooCommerce updates
    DEFAULT_BATCH_SIZE = 50
    
    # Default timeout for API requests (seconds)
    DEFAULT_TIMEOUT = 30
    
    # Default retry configuration
    DEFAULT_RETRY_TOTAL = 3
    DEFAULT_RETRY_BACKOFF_FACTOR = 1
    DEFAULT_RETRY_STATUS_FORCELIST = [429, 500, 502, 503, 504]
    
    # WooCommerce location attribute ID (verify this matches your store)
    LOCATION_ATTRIBUTE_ID = 28
    
    @staticmethod
    def get_config() -> Dict[str, Optional[str]]:
        """
        Get configuration from environment variables.
        
        Returns:
            Dict containing configuration values
            
        Raises:
            ValueError: If required environment variables are missing
        """
        config = {
            'vendor_api_url': os.environ.get('VENDOR_API_URL'),
            'external_api_username': os.environ.get('EXTERNAL_API_USERNAME'),
            'external_api_password': os.environ.get('EXTERNAL_API_PASSWORD'),
            'wp_url': os.environ.get('WP_URL'),
            'consumer_key': os.environ.get('CONSUMER_KEY'),
            'consumer_secret': os.environ.get('CONSUMER_SECRET')
        }
        
        # Check for required environment variables
        missing_vars = [key for key, value in config.items() if not value]
        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}. "
                f"Please check your .env file."
            )
        
        return config
    
    @staticmethod
    def validate_config(config: Dict[str, str]) -> bool:
        """
        Validate configuration values.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        # Validate URLs
        if not config['vendor_api_url'].startswith(('http://', 'https://')):
            raise ValueError("VENDOR_API_URL must be a valid HTTP/HTTPS URL")
            
        if not config['wp_url'].startswith(('http://', 'https://')):
            raise ValueError("WP_URL must be a valid HTTP/HTTPS URL")
        
        # Validate WooCommerce credentials format
        if not config['consumer_key'].startswith('ck_'):
            raise ValueError("CONSUMER_KEY should start with 'ck_'")
            
        if not config['consumer_secret'].startswith('cs_'):
            raise ValueError("CONSUMER_SECRET should start with 'cs_'")
        
        return True
