#!/usr/bin/env python3
"""
Example usage of the Stock Diff Location Sync tool.

This script demonstrates how to use the StockDiffSync class
with proper error handling and logging.
"""

import sys
import logging
from config import Config
from stoc_diff_loc import StockDiffSync


def setup_logging():
    """Set up logging configuration for the example."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """Main example function."""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Load and validate configuration
        logger.info("Loading configuration...")
        config = Config.get_config()
        Config.validate_config(config)
        logger.info("Configuration loaded successfully")
        
        # Initialize the sync tool
        logger.info("Initializing Stock Diff Sync...")
        syncer = StockDiffSync(config)
        
        # Perform the sync
        logger.info("Starting synchronization...")
        syncer.sync()
        logger.info("Synchronization completed successfully")
        
    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        logger.error("Please check your .env file and ensure all required variables are set")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"Sync failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
