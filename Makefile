.PHONY: help install test lint clean run setup

help:
	@echo "Available commands:"
	@echo "  setup     - Set up development environment"
	@echo "  install   - Install dependencies"
	@echo "  test      - Run tests"
	@echo "  lint      - Run linting"
	@echo "  clean     - Clean up generated files"
	@echo "  run       - Run the sync tool"
	@echo "  example   - Run the example script"

setup:
	python -m venv venv
	@echo "Virtual environment created. Activate with:"
	@echo "  source venv/bin/activate  # On Linux/Mac"
	@echo "  venv\\Scripts\\activate     # On Windows"

install:
	pip install -r requirements.txt
	pip install pytest pytest-cov flake8 safety bandit

test:
	python -m pytest tests/ -v

test-cov:
	python -m pytest tests/ --cov=. --cov-report=html --cov-report=term

lint:
	flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
	flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

security:
	safety check
	bandit -r . -x tests/

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .pytest_cache/
	rm -rf *.egg-info/
	rm -f stock_sync_*.log

run:
	python stoc_diff_loc.py

example:
	python example.py

all: lint test security
